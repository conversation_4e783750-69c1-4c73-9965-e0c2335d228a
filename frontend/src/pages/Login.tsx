import React from 'react';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import LoginForm from '@/components/LoginForm';
import { NavLink } from 'react-router-dom';

const Login = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <RRUHeader />
      <Header />

      <main className="flex-grow py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Simple heading with gold underline */}
            <div className="text-center mb-12">
              <h1 className="text-3xl md:text-4xl font-bold text-gyaan-navy mb-6">
                Login to Your Account
              </h1>
              <div className="w-24 h-1 bg-gyaan-gold mx-auto"></div>
            </div>

            <div className="flex justify-center">
              <LoginForm />
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Login;
