import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import RegistrationForm from '@/components/RegistrationForm';

const Register = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <RRUHeader />
      <Header />

      <main className="flex-grow py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-bold text-gyaan-navy mb-6">
              Candidate Registration
            </h1>
            <div className="w-24 h-1 bg-gyaan-gold mx-auto"></div>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Registration Process - Left Side */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
                  <h2 className="text-xl font-semibold mb-4 text-gyaan-navy">
                    Registration Process
                  </h2>
                  <div className="w-16 h-1 bg-gyaan-gold mb-4"></div>
                  <p className="text-gray-600 text-sm mb-6">
                    Follow these steps to complete your registration and start
                    your learning journey with GyaanRaksha Samyog.
                  </p>
                  <ol className="list-decimal list-inside space-y-2 text-gray-700 mb-6 text-sm">
                    <li>
                      Complete the registration form with your personal details
                    </li>
                    <li>Verify your email address and mobile number</li>
                    <li>Log in to your account to complete your profile</li>
                    <li>Select courses and submit required documents</li>
                    <li>Pay applicable fees and start your learning journey</li>
                  </ol>
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <p className="text-sm text-gray-600">
                      For assistance with registration, please contact our
                      helpdesk at{' '}
                      <a
                        href="mailto:<EMAIL>"
                        className="text-gyaan-maroon font-medium hover:underline"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              {/* Registration Form - Right Side */}
              <div className="lg:col-span-2">
                <RegistrationForm />
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Register;
