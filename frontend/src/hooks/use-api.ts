import { useState, useEffect, useCallback } from 'react';
import { AxiosError, AxiosResponse } from 'axios';
import { useToast } from '@/hooks/use-toast';
import { getErrorMessage, handleApiError } from '@/utils/api-utils';
import { APP_CONFIG } from '@/config/environment';

interface UseApiOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: AxiosError) => void;
  enabled?: boolean;
  initialData?: T;
  errorMessage?: string;
  pollingInterval?: number; // Time in milliseconds between polling requests, undefined or 0 means no polling
  // Mock data is no longer supported
  // These parameters are kept for backward compatibility but are ignored
}

interface UseApiResult<T> {
  data: T | null;
  isLoading: boolean;
  error: AxiosError | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook for making API calls with loading, error, and data states
 * @param apiCall - Function that returns a Promise from an API call
 * @param options - Configuration options
 * @returns Object containing data, loading state, error, and refetch function
 */
export function useApi<T>(
  apiCall: () => Promise<AxiosResponse<{ data: T }>>,
  options: UseApiOptions<T> = {}
): UseApiResult<T> {
  const [data, setData] = useState<T | null>(options.initialData || null);
  // Only set initial loading to true if enabled and no initial data
  const [isLoading, setIsLoading] = useState<boolean>(
    options.enabled !== false && !options.initialData
  );
  const [error, setError] = useState<AxiosError | null>(null);
  const { toast } = useToast();

  // Add a debounce for loading state to prevent flickering
  const [shouldShowLoading, setShouldShowLoading] = useState<boolean>(false);

  useEffect(() => {
    // Only show loading indicator if loading takes more than 300ms
    let timer: NodeJS.Timeout;
    if (isLoading) {
      timer = setTimeout(() => {
        setShouldShowLoading(true);
      }, 300);
    } else {
      setShouldShowLoading(false);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isLoading]);

  // Track the last fetch time to prevent excessive calls
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  const fetchData = useCallback(async () => {
    // Implement a debounce to prevent rapid successive calls
    const now = Date.now();
    const minInterval = 1000; // 1 second minimum between calls

    if (now - lastFetchTime < minInterval) {
      console.log('Skipping API call - too soon after last call');
      return;
    }

    // Don't set loading if we already have data to prevent UI flicker on refetch
    if (!data) {
      setIsLoading(true);
    }
    setError(null);

    // Update last fetch time
    setLastFetchTime(now);

    try {
      const response = await apiCall();

      // Handle different response structures
      let responseData;

      // Check if response has the expected structure
      if (response.data && response.data.data !== undefined) {
        // Standard API response structure: { success: true, data: {...} }
        responseData = response.data.data;
      } else if (response.data !== undefined) {
        // Direct API response or simplified structure
        responseData = response.data;
      } else if (response !== undefined) {
        // Direct response (no data wrapper)
        responseData = response;
      } else {
        // Fallback for unexpected response structure
        console.warn('Unexpected API response structure:', response);
        responseData = null;
      }

      // Log the response in development mode
      if (APP_CONFIG.isDevelopment) {
        console.log('API Response:', {
          url: response.config?.url,
          method: response.config?.method,
          status: response.status,
          data: responseData,
        });
      }

      // Handle empty data case
      if (
        responseData === null ||
        (Array.isArray(responseData) && responseData.length === 0) ||
        (typeof responseData === 'object' &&
          Object.keys(responseData).length === 0)
      ) {
        console.warn('API returned empty data:', response.config?.url);
      }

      setData(responseData);

      if (options.onSuccess) {
        options.onSuccess(responseData);
      }
    } catch (err) {
      const axiosError = err as AxiosError;

      // Don't set error for 304 Not Modified responses
      if (axiosError.response?.status === 304) {
        return;
      }

      // No mock data fallback - we always handle errors properly
      // This ensures a consistent user experience with proper error states

      setError(axiosError);

      // Log error in development mode
      if (APP_CONFIG.isDevelopment) {
        console.error('API Error:', err);
        console.error('API Error Details:', {
          url: axiosError.config?.url,
          method: axiosError.config?.method,
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          message: axiosError.message,
          responseData: axiosError.response?.data,
        });
      }

      // Call onError callback if provided
      if (options.onError) {
        options.onError(axiosError);
      }
      // Show error message if provided
      else if (options.errorMessage) {
        // Don't show toast for application trends error - we'll handle it in the UI
        if (!axiosError.config?.url?.includes('trends/applications')) {
          toast({
            title: 'Error',
            description: options.errorMessage,
            variant: 'destructive',
          });
        }
      }
      // Show generic error message
      else {
        // Don't show toast for application trends error - we'll handle it in the UI
        if (!axiosError.config?.url?.includes('trends/applications')) {
          // Use the utility function to handle the error
          const errorMessage = getErrorMessage(err);
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'destructive',
          });
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [apiCall, options, toast, data, lastFetchTime]);

  useEffect(() => {
    // Only fetch data if enabled and not disabled by polling options
    if (options.enabled !== false) {
      // Initial fetch
      fetchData();

      // Set up polling if specified
      if (options.pollingInterval && options.pollingInterval > 0) {
        const intervalId = setInterval(() => {
          fetchData();
        }, options.pollingInterval);

        // Clean up interval on unmount
        return () => clearInterval(intervalId);
      }
    }
  }, [fetchData, options.enabled, options.pollingInterval]);

  // Return shouldShowLoading instead of isLoading to prevent UI flicker
  return {
    data,
    isLoading: shouldShowLoading,
    error,
    refetch: fetchData,
  };
}

interface UseMutationOptions<T, R> {
  onSuccess?: (data: R) => void;
  onError?: (error: AxiosError) => void;
  successMessage?: string;
  errorMessage?: string;
}

interface UseMutationResult<T, R> {
  mutate: (variables: T) => Promise<R | null>;
  isLoading: boolean;
  error: AxiosError | null;
  data: R | null;
}

/**
 * Custom hook for making mutation API calls (POST, PUT, DELETE)
 * @param mutationFn - Function that takes variables and returns a Promise from an API call
 * @param options - Configuration options
 * @returns Object containing mutate function, loading state, error, and data
 */
export function useMutation<T, R>(
  mutationFn: (variables: T) => Promise<AxiosResponse<{ data: R }>>,
  options: UseMutationOptions<T, R> = {}
): UseMutationResult<T, R> {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<AxiosError | null>(null);
  const [data, setData] = useState<R | null>(null);
  const { toast } = useToast();

  const mutate = async (variables: T): Promise<R | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await mutationFn(variables);

      // Handle different response structures
      let responseData;

      // Check if response has the expected structure
      if (response.data && response.data.data !== undefined) {
        // Standard API response structure
        responseData = response.data.data;
      } else if (response.data !== undefined) {
        // Mock data or simplified API response
        responseData = response.data;
      } else {
        // Fallback for unexpected response structure
        console.warn('Unexpected API response structure:', response);
        responseData = null;
      }

      setData(responseData);

      if (options.successMessage) {
        toast({
          title: 'Success',
          description: options.successMessage,
        });
      }

      if (options.onSuccess) {
        options.onSuccess(responseData);
      }

      return responseData;
    } catch (err) {
      const axiosError = err as AxiosError;
      setError(axiosError);

      // Log error in development mode
      if (APP_CONFIG.isDevelopment) {
        console.error('Mutation Error:', err);
      }

      if (options.onError) {
        options.onError(axiosError);
      } else if (options.errorMessage) {
        toast({
          title: 'Error',
          description: options.errorMessage,
          variant: 'destructive',
        });
      } else {
        // Use the utility function to handle the error
        const errorMessage = getErrorMessage(err);
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      }

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return { mutate, isLoading, error, data };
}

/**
 * Custom hook for infinite scrolling with API pagination
 * @param apiCall - Function that takes page and limit and returns a Promise from an API call
 * @param options - Configuration options
 * @returns Object containing data, loading states, error, and functions to load more data
 */
export function useInfiniteApi<T>(
  apiCall: (
    page: number,
    limit: number
  ) => Promise<
    AxiosResponse<{
      data: T[];
      meta: { total: number; page: number; limit: number };
    }>
  >,
  options: {
    limit?: number;
    onSuccess?: (data: T[]) => void;
    onError?: (error: AxiosError) => void;
    enabled?: boolean;
    initialData?: T[];
  } = {}
) {
  const [data, setData] = useState<T[]>(options.initialData || []);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(
    options.enabled !== false
  );
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<AxiosError | null>(null);
  const limit = options.limit || 10;
  const { toast } = useToast();

  const fetchData = useCallback(
    async (currentPage: number, replace = false) => {
      if (currentPage === 1) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      setError(null);

      try {
        const response = await apiCall(currentPage, limit);
        const newData = response.data.data;
        const meta = response.data.meta;

        setData((prevData) => (replace ? newData : [...prevData, ...newData]));
        setHasMore(meta.page * meta.limit < meta.total);

        if (options.onSuccess) {
          options.onSuccess(newData);
        }
      } catch (err) {
        const axiosError = err as AxiosError;
        setError(axiosError);

        // Log error in development mode
        if (APP_CONFIG.isDevelopment) {
          console.error('Infinite API Error:', err);
        }

        if (options.onError) {
          options.onError(axiosError);
        } else {
          // Use the utility function to handle the error
          const errorMessage = getErrorMessage(
            err,
            'Failed to load data. Please try again.'
          );
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'destructive',
          });
        }
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    [apiCall, limit, options, toast]
  );

  useEffect(() => {
    if (options.enabled !== false) {
      fetchData(1, true);
    }
  }, [fetchData, options.enabled]);

  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchData(nextPage);
    }
  }, [fetchData, hasMore, isLoadingMore, page]);

  const refresh = useCallback(() => {
    setPage(1);
    fetchData(1, true);
  }, [fetchData]);

  return {
    data,
    isLoading,
    isLoadingMore,
    error,
    hasMore,
    loadMore,
    refresh,
  };
}

export default useApi;
