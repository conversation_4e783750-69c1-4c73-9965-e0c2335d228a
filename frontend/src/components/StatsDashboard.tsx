import React, { useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  AlertCircle,
  Users,
  Building,
  GraduationCap,
  Award,
} from 'lucide-react';
import { useApi } from '@/hooks/use-api';
import { statsAPI } from '@/services/api';

type StatCardProps = {
  count: string;
  label: string;
  icon: React.ReactNode;
};

const StatCard = ({ count, label, icon }: StatCardProps) => (
  <div className="bg-white border border-gray-200 rounded-lg p-6 text-center shadow-lg">
    <div className="bg-gyaan-navy p-4 rounded-full mb-4 mx-auto w-fit">
      {icon}
    </div>
    <p className="text-4xl font-bold text-gyaan-navy mb-2">{count}</p>
    <p className="text-lg text-gray-700 font-semibold">{label}</p>
  </div>
);

// Empty state component for when no data is available
const EmptyState = () => (
  <div className="col-span-1 md:col-span-4 py-8 text-center">
    <div className="flex flex-col items-center justify-center">
      <AlertCircle className="h-12 w-12 text-gray-300 mb-4" />
      <h3 className="text-lg font-medium text-gray-500 mb-1">
        No Data Available
      </h3>
      <p className="text-gray-400 max-w-md mx-auto">
        Statistics are currently unavailable. Please check back later.
      </p>
    </div>
  </div>
);

// Loading state component
const LoadingState = () => (
  <>
    {[...Array(4)].map((_, index) => (
      <div key={index} className="bg-white rounded-lg p-6 shadow-md">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div>
            <Skeleton className="h-8 w-24 mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>
    ))}
  </>
);

// Define the type for our stats data
interface StatsData {
  partnersCount: number;
  institutesCount: number;
  candidatesCount: number;
  degreesCount: number;
}

// Fallback data to use when API fails
const fallbackData: StatsData = {
  partnersCount: 12,
  institutesCount: 25,
  candidatesCount: 5000,
  degreesCount: 3200,
};

const StatsDashboard = () => {
  // State to track if we're using fallback data
  const [usingFallback, setUsingFallback] = useState(false);

  // Fetch stats data from API
  const {
    data: apiStatsData,
    isLoading,
    error,
  } = useApi<StatsData>(() => statsAPI.getStats(), {
    onError: (error) => {
      console.error('Failed to load stats data:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
      });
      setUsingFallback(true);
    },
  });

  // Use API data if available, otherwise use fallback data
  const statsData =
    usingFallback || error || !apiStatsData ? fallbackData : apiStatsData;

  // Define icons for stats
  const getStatsWithIcons = () => {
    return [
      {
        count: `${statsData.partnersCount}+`,
        label: 'MoU Partners',
        icon: <Users className="h-8 w-8 text-white" />,
      },
      {
        count: `${statsData.institutesCount}+`,
        label: 'Accredited Institutes',
        icon: <Building className="h-8 w-8 text-white" />,
      },
      {
        count: `${statsData.candidatesCount.toLocaleString()}+`,
        label: 'Registered Candidates',
        icon: <GraduationCap className="h-8 w-8 text-white" />,
      },
      {
        count: `${statsData.degreesCount.toLocaleString()}+`,
        label: 'Degree/Certificate Awarded',
        icon: <Award className="h-8 w-8 text-white" />,
      },
    ];
  };

  // Get stats with icons
  const stats = getStatsWithIcons();

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gyaan-navy mb-4">
            Our Impact
          </h2>
          <div className="w-24 h-1 bg-gyaan-gold mx-auto"></div>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {isLoading && !usingFallback ? (
              <LoadingState />
            ) : stats.length === 0 ? (
              <EmptyState />
            ) : (
              stats.map((stat, index) => <StatCard key={index} {...stat} />)
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default StatsDashboard;
