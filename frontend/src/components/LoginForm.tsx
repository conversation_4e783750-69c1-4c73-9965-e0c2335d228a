import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/hooks/use-toast';
import { NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '@/providers/AuthProvider';
import { LoginCredentials } from '@/types/api';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2 } from 'lucide-react';

const candidateSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

const adminSchema = z.object({
  username: z.string().min(1, { message: 'Username is required' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

type UserType = 'candidate' | 'admin';

const LoginForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userType, setUserType] = useState<UserType>('candidate');
  const { toast } = useToast();
  const { login } = useAuth();
  const navigate = useNavigate();

  const candidateForm = useForm<z.infer<typeof candidateSchema>>({
    resolver: zodResolver(candidateSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const adminForm = useForm<z.infer<typeof adminSchema>>({
    resolver: zodResolver(adminSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const onCandidateSubmit = async (values: z.infer<typeof candidateSchema>) => {
    setIsSubmitting(true);
    try {
      // Call the login function from AuthContext with credentials
      const credentials: LoginCredentials = {
        email: values.email,
        password: values.password,
      };

      const success = await login(credentials);

      // Navigation is handled in the login function
      if (!success) {
        // Reset the form if login failed
        candidateForm.reset();
      }
    } catch (error: any) {
      console.error('Login error:', error);

      toast({
        title: 'Login Failed',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onAdminSubmit = async (values: z.infer<typeof adminSchema>) => {
    setIsSubmitting(true);
    try {
      // Call the login function from AuthContext with admin credentials
      const credentials: LoginCredentials = {
        email: values.username, // Using username as email for admin login
        password: values.password,
        isAdmin: true, // Add a flag to indicate admin login
      };

      const success = await login(credentials);

      // Navigation is handled in the login function
      if (!success) {
        // Reset the form if login failed
        adminForm.reset();
      }
    } catch (error: any) {
      console.error('Admin login error:', error);

      // Get more specific error message if available
      let errorMessage = 'Invalid username or password. Please try again.';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      toast({
        title: 'Login Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTabChange = (value: string) => {
    setUserType(value as UserType);
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-lg max-w-4xl w-full mx-auto border border-gray-200">
      <Tabs defaultValue="candidate" onValueChange={handleTabChange}>
        <TabsList className="grid grid-cols-2 mb-8 max-w-md mx-auto">
          <TabsTrigger value="candidate" className="py-2">
            Candidate Login
          </TabsTrigger>
          <TabsTrigger value="admin" className="py-2">
            Admin Login
          </TabsTrigger>
        </TabsList>

        <TabsContent value="candidate">
          <Form {...candidateForm}>
            <form
              onSubmit={candidateForm.handleSubmit(onCandidateSubmit)}
              className="space-y-6"
            >
              <div className="space-y-4">
                <FormField
                  control={candidateForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gyaan-navy font-medium">
                        Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your email"
                          {...field}
                          className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={candidateForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gyaan-navy font-medium">
                        Password
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Enter your password"
                          {...field}
                          className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-between items-center">
                <a
                  href="#"
                  className="text-gyaan-maroon hover:underline text-sm"
                >
                  Forgot password?
                </a>

                <Button
                  type="submit"
                  className="bg-gyaan-navy hover:bg-gyaan-navy/90 text-white px-8 py-2"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Logging in...
                    </>
                  ) : (
                    'Login'
                  )}
                </Button>
              </div>

              <div className="text-center text-sm mt-4">
                <p className="text-gray-600">
                  Don't have an account?{' '}
                  <NavLink
                    to="/register"
                    className="text-gyaan-maroon hover:underline font-medium"
                  >
                    Register here
                  </NavLink>
                </p>
              </div>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="admin">
          <Form {...adminForm}>
            <form
              onSubmit={adminForm.handleSubmit(onAdminSubmit)}
              className="space-y-6"
            >
              <div className="space-y-4">
                <FormField
                  control={adminForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gyaan-navy font-medium">
                        Username
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your username"
                          {...field}
                          className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={adminForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gyaan-navy font-medium">
                        Password
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Enter your password"
                          {...field}
                          className="border border-gray-300 focus:border-gyaan-navy focus:ring-1 focus:ring-gyaan-navy"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-between items-center">
                <a
                  href="#"
                  className="text-gyaan-maroon hover:underline text-sm"
                >
                  Forgot password?
                </a>

                <Button
                  type="submit"
                  className="bg-gyaan-maroon hover:bg-gyaan-maroon/90 text-white px-8 py-2"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Logging in...
                    </>
                  ) : (
                    'Login as Admin'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LoginForm;
