import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';

const formSchema = z.object({
  isCurrentlyServing: z.string(),
  policeForce: z
    .string()
    .min(1, { message: 'Police force type is required' })
    .optional(),
  organizationType: z
    .string()
    .min(1, { message: 'Organization type is required' }),
  organizationName: z
    .string()
    .min(1, { message: 'Organization name is required' }),
  designation: z.string().min(1, { message: 'Designation is required' }),
  rank: z.string().min(1, { message: 'Rank is required' }),
  employeeId: z.string().min(1, { message: 'Employee ID is required' }),
  dateOfJoining: z.date({ required_error: 'Date of joining is required' }),
  yearsOfService: z.coerce
    .number()
    .min(0, { message: 'Years of service is required' }),
  currentPosting: z.string().min(1, { message: 'Current posting is required' }),
  serviceRemarks: z.string().optional(),
});

interface ServiceDetailsFormProps {
  defaultValues?: any;
  onSubmit: (data: any) => void;
  onBack: () => void;
}

interface ServiceDetailsFormProps {
  defaultValues?: any;
  onSubmit: (data: any) => void;
  onBack: () => void;
  userCategory?: string;
}

export const ServiceDetailsForm: React.FC<ServiceDetailsFormProps> = ({
  defaultValues = {},
  onSubmit,
  onBack,
  userCategory = 'civilian',
}) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      isCurrentlyServing: defaultValues.isCurrentlyServing || 'yes',
      policeForce: defaultValues.policeForce || '',
      organizationType: defaultValues.organizationType || '',
      organizationName: defaultValues.organizationName || '',
      designation: defaultValues.designation || '',
      rank: defaultValues.rank || '',
      employeeId: defaultValues.employeeId || '',
      dateOfJoining: defaultValues.dateOfJoining
        ? new Date(defaultValues.dateOfJoining)
        : undefined,
      yearsOfService: defaultValues.yearsOfService || 0,
      currentPosting: defaultValues.currentPosting || '',
      serviceRemarks: defaultValues.serviceRemarks || '',
    },
  });

  const isCurrentlyServing = form.watch('isCurrentlyServing');
  const organizationType = form.watch('organizationType');
  const isPoliceOrCapf =
    userCategory === 'state-police' || userCategory === 'capf';

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    onSubmit(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-4">Service Details</h3>

          {isPoliceOrCapf ? (
            <div className="bg-blue-50 p-4 rounded-md mb-6">
              <p className="text-blue-700 text-sm">
                As a {userCategory === 'state-police' ? 'State Police' : 'CAPF'}{' '}
                candidate, please provide your service details below.
              </p>
            </div>
          ) : (
            <FormField
              control={form.control}
              name="isCurrentlyServing"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>
                    Are you currently serving in any organization?
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="yes" />
                        </FormControl>
                        <FormLabel className="font-normal">Yes</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="no" />
                        </FormControl>
                        <FormLabel className="font-normal">No</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {(isPoliceOrCapf || isCurrentlyServing === 'yes') && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              {isPoliceOrCapf && (
                <FormField
                  control={form.control}
                  name="policeForce"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Police Force Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select police force type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="state-police">
                            State Police
                          </SelectItem>
                          <SelectItem value="capf">CAPF</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="organizationType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select organization type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="state-police">
                          State Police
                        </SelectItem>
                        <SelectItem value="central-police">
                          Central Police Organization
                        </SelectItem>
                        <SelectItem value="capf">
                          Central Armed Police Forces
                        </SelectItem>
                        <SelectItem value="defense">Defense Forces</SelectItem>
                        <SelectItem value="government">
                          Government Department
                        </SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="organizationName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter organization name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rank"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rank</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your rank" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="designation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Designation</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your designation" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Employee ID / Service Number</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your ID/service number"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dateOfJoining"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date of Joining</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP')
                            ) : (
                              <span>Select date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date('1970-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="yearsOfService"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Years of Service</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter years of service"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="currentPosting"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Posting</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your current posting location"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="serviceRemarks"
                render={({ field }) => (
                  <FormItem className="col-span-2">
                    <FormLabel>Additional Remarks (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any additional information about your service"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {(isPoliceOrCapf ||
                organizationType === 'state-police' ||
                organizationType === 'capf' ||
                organizationType === 'central-police') && (
                <div className="col-span-2 bg-yellow-50 p-4 rounded-md">
                  <p className="text-yellow-800 text-sm">
                    <strong>Note:</strong> You will need to upload a No
                    Objection Certificate (NOC) from your organization in the
                    document upload section.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit">Next Step</Button>
        </div>
      </form>
    </Form>
  );
};
